//tools.js
Page({
  data: {
    // 搜索关键词
    searchKeyword: '',

    // 当前筛选条件
    currentFilter: 'all',

    // 当前子区域筛选条件
    currentSubFilter: 'all',

    // 当前显示的子区域列表
    subAreas: [],

    // 各区域对应的子区域数据
    subAreaData: {
      nanshan: ['蛇口', '前海', '华侨城', '科技园', '深圳湾', '南山中心', '后海', '红树湾', '南头', '大学城', '西丽', '白石洲'],
      baoan: ['西乡', '宝安中心区', '碧海', '西南', '翻身', '航城', '桃源居', '曦城', '福永', '沙井', '松岗', '石岩'],
      futian: ['景田', '香梅北', '香蜜湖', '安托山', '赤尾', '上下沙', '皇岗', '百花', '莲花', '新洲', '石厦', '竹子林', '八卦岭', '梅林', '黄木岗', '华强北', '保税区', '园岭', '福田中心区', '沙尾', '车公庙', '上步', '华强南'],
      luohu: ['地王', '万象城', '笋岗', '螺岭', '黄贝岭', '布心', '百仕达', '翠竹', '春风路', '清水河', '新秀', '莲塘', '罗湖口岸', '东门', '洪湖', '银湖'],
      longhua: ['红山', '上塘', '民治', '龙华中心', '观澜'],
      longgang: ['坂田', '大运', '双龙', '龙岗中心城', '布吉关', '横岗', '坪地', '布吉大芬', '布吉街', '布吉南岭', '布吉水径', '石芽岭', '丹竹头', '平湖'],
      yantian: ['梅沙', '盐田港', '沙头角', '中英街', '光明', '坪山', '大鹏']
    },

    // 房源数据列表
    propertyList: [
      {
        id: 1,
        name: '钜建大厦',
        lao:'后海',
        layout: '1房2厅',
        floor: '19/24',
        price: '398',
        date: '2025/04/15签约',
        area: ' 87.0',
        unitPrice: ' 87',
        count:'2',
        lpp:'(2020)',
      },
      {
        id: 2,
        name: '京基御景华城',
        lao:'布心',
        layout: '1房',
        floor: '3/16',
        price: '141',
        date: '2025/04/15签约',
        area: ' 35.0',
        unitPrice: ' 87',
      },
      {
        id: 3,
        name: '临大小区',
        lao:'瓷尾',
        layout: '2房',
        floor: '5/8',
        price: '346',
        date: '2025/04/15签约',
        area: ' 49.0',
        unitPrice: ' 87',
      },
      {
        id: 4,
        name: '合正J168',
        layout: '1房',
        lao:'翠竹',
        floor: '4/24',
        price: '130',
        date: '2025/04/15签约',
        area: ' 23.0',
        unitPrice: ' 87',
 
      },
      {
        id: 5,
        name: '今日家园',
        layout: '14/25',
        floor: '14/25',
        lao:'螺岭',
        price: '390',
        date: '2025/04/15签约',
        area: ' 62.0',
        unitPrice: ' 87',
      },
      {
        id: 6,
        name: '长丰苑',
        layout: '3房',
        lao:'春分路',
        floor: '6/29',
        price: '1307',
        date: '2025/04/15签约',
        area: ' 61.0',
        unitPrice: ' 87',
      }
    ],

    // 原始数据备份（用于筛选）
    originalPropertyList: []
  },

  onLoad() {
    console.log('深圳二手房成交数据页面加载完成');

    // 备份原始数据
    this.setData({
      originalPropertyList: this.data.propertyList
    });

    // 预留：页面加载时调用API获取数据
    this.loadPropertyData();
  },

  // 预留：加载房源数据API接口
  loadPropertyData() {
    console.log('加载房源数据');

    // TODO: 预留API接口调用位置
    // 后续可以在这里调用微信公众号API获取房源数据
    /*
    wx.request({
      url: 'https://your-api.com/api/property/list',
      method: 'GET',
      data: {
        filter: this.data.currentFilter,
        keyword: this.data.searchKeyword
      },
      success: (res) => {
        console.log('房源数据加载成功:', res.data);
        this.setData({
          propertyList: res.data.list || [],
          originalPropertyList: res.data.list || []
        });
      },
      fail: (err) => {
        console.error('房源数据加载失败:', err);
        wx.showToast({
          title: '数据加载失败',
          icon: 'none'
        });
      }
    });
    */
  },

  // 搜索输入事件
  onSearchInput(e) {
    const keyword = e.detail.value;
    this.setData({
      searchKeyword: keyword
    });

    // 实时搜索
    this.performSearch(keyword);
  },

  // 搜索确认事件
  onSearchConfirm(e) {
    const keyword = e.detail.value;
    console.log('搜索确认:', keyword);

    this.performSearch(keyword);

    // 预留：API搜索接口调用
    this.callSearchAPI(keyword);
  },

// 执行搜索
performSearch(keyword) {
  if (!keyword.trim()) {
    // 如果搜索关键词为空，显示所有数据
    this.setData({
      propertyList: this.data.originalPropertyList
    });
    return;
  }

  // 本地搜索过滤
  const filteredList = this.data.originalPropertyList.filter(item => {
    return item.name.includes(keyword) ||
           item.layout.includes(keyword) ||
           item.area.includes(keyword);
  });

  this.setData({
    propertyList: filteredList
  });

  console.log(`搜索"${keyword}"，找到${filteredList.length}条结果`);
},

// 预留：调用搜索API接口
callSearchAPI(keyword) {
  console.log('准备调用搜索API:', keyword);

  // TODO: 预留搜索API接口调用
  /*
  wx.request({
    url: 'https://your-api.com/api/property/search',
    method: 'POST',
    data: {
      keyword: keyword,
      filter: this.data.currentFilter
    },
    success: (res) => {
      console.log('搜索API调用成功:', res.data);
      this.setData({
        propertyList: res.data.list || []
      });
    },
    fail: (err) => {
      console.error('搜索API调用失败:', err);
    }
  });
  */
},

// 筛选标签点击事件
onFilterTap(e) {
  const filter = e.currentTarget.dataset.filter;

  // 获取对应的子区域数据
  const subAreas = this.data.subAreaData[filter] || [];

  this.setData({
    currentFilter: filter,
    currentSubFilter: 'all', // 重置子区域筛选
    subAreas: subAreas
  });

  console.log('切换筛选条件:', filter);
  console.log('子区域列表:', subAreas);

  // 执行筛选
  this.performFilter(filter, 'all');

  // 预留：API筛选接口调用
  this.callFilterAPI(filter);
},

// 子区域筛选标签点击事件
onSubFilterTap(e) {
  const subFilter = e.currentTarget.dataset.subfilter;

  this.setData({
    currentSubFilter: subFilter
  });

  console.log('切换子区域筛选条件:', subFilter);

  // 执行筛选
  this.performFilter(this.data.currentFilter, subFilter);

  // 预留：API筛选接口调用
  this.callSubFilterAPI(this.data.currentFilter, subFilter);
},

// 执行筛选
performFilter(filter, subFilter = 'all') {
  let filteredList = this.data.originalPropertyList;

  if (filter !== 'all') {
    // 根据筛选条件过滤数据
    // 这里可以根据实际需求添加筛选逻辑
    console.log(`按${filter}筛选数据`);

    // 示例：根据区域筛选（需要在数据中添加区域字段）
    // filteredList = this.data.originalPropertyList.filter(item => {
    //   return item.district === filter;
    // });
  }

  if (subFilter !== 'all') {
    // 根据子区域筛选条件进一步过滤数据
    console.log(`按子区域${subFilter}筛选数据`);

    // 示例：根据子区域筛选（需要在数据中添加子区域字段）
    // filteredList = filteredList.filter(item => {
    //   return item.subDistrict === subFilter;
    // });
  }

  this.setData({
    propertyList: filteredList
  });
},

// 预留：调用筛选API接口
callFilterAPI(filter) {
  console.log('准备调用筛选API:', filter);

  // TODO: 预留筛选API接口调用
  /*
  wx.request({
    url: 'https://your-api.com/api/property/filter',
    method: 'POST',
    data: {
      filter: filter,
      keyword: this.data.searchKeyword
    },
    success: (res) => {
      console.log('筛选API调用成功:', res.data);
      this.setData({
        propertyList: res.data.list || []
      });
    },
    fail: (err) => {
      console.error('筛选API调用失败:', err);
    }
  });
  */
},

// 预留：调用子区域筛选API接口
callSubFilterAPI(filter, subFilter) {
  console.log('准备调用子区域筛选API:', filter, subFilter);

  // TODO: 预留子区域筛选API接口调用
  /*
  wx.request({
    url: 'https://your-api.com/api/property/subfilter',
    method: 'POST',
    data: {
      filter: filter,
      subFilter: subFilter,
      keyword: this.data.searchKeyword
    },
    success: (res) => {
      console.log('子区域筛选API调用成功:', res.data);
      this.setData({
        propertyList: res.data.list || []
      });
    },
    fail: (err) => {
      console.error('子区域筛选API调用失败:', err);
    }
  });
  */
},

// 房源项点击事件
onPropertyTap(e) {
  const property = e.currentTarget.dataset.property;

  console.log('点击房源:', property);

  // 显示点击反馈
  wx.showToast({
    title: `查看${property.name}详情`,
    icon: 'none',
    duration: 1500
  });

  // 预留：API接口调用位置
  this.callPropertyDetailAPI(property);
},

// 预留：调用房源详情API接口
callPropertyDetailAPI(property) {
  console.log('准备调用房源详情API:', property);

  // TODO: 预留房源详情API接口调用
  /*
  wx.request({
    url: 'https://your-api.com/api/property/detail',
    method: 'POST',
    data: {
      id: property.id,
      name: property.name
    },
    success: (res) => {
      console.log('房源详情API调用成功:', res.data);
      // 可以跳转到详情页面或显示详情弹窗
      // wx.navigateTo({
      //   url: `/pages/property-detail/property-detail?id=${property.id}`
      // });
    },
    fail: (err) => {
      console.error('房源详情API调用失败:', err);
    }
  });
  */
},

// 底部导航点击事件
onNavTap(e) {
  const nav = e.currentTarget.dataset.nav;

  console.log('点击底部导航:', nav);

  switch(nav) {
    case 'search':
      // 成交查询 - 当前页面，不需要跳转
      wx.showToast({
        title: '当前页面',
        icon: 'none'
      });
      break;
    case 'strategy':
      // 买房攻略 - 跳转到攻略页面
      wx.navigateTo({
        url: '/pages/terms/terms'
      });
      break;
    case 'service':
      // 工具服务 - 跳转到服务页面
      wx.showToast({
        title: '工具服务功能开发中',
        icon: 'none'
      });
      break;
    default:
      console.log('未知导航项:', nav);
  }

  // 预留：API接口调用位置
  this.callNavigationAPI(nav);
},

// 预留：调用导航API接口
callNavigationAPI(nav) {
  console.log('准备调用导航API:', nav);

  // TODO: 预留导航API接口调用
  // 可以用于统计用户行为或获取相关数据
  /*
  wx.request({
    url: 'https://your-api.com/api/navigation/track',
    method: 'POST',
    data: {
      action: nav,
      timestamp: Date.now(),
      page: 'tools'
    },
    success: (res) => {
      console.log('导航API调用成功:', res.data);
    },
    fail: (err) => {
      console.error('导航API调用失败:', err);
    }
  });
  */
},

// 预留：刷新数据
onRefresh() {
  console.log('刷新数据');

  // 重置搜索和筛选条件
  this.setData({
    searchKeyword: '',
    currentFilter: 'all',
    currentSubFilter: 'all',
    subAreas: [],
    propertyList: this.data.originalPropertyList
  });

  // 重新加载数据
  this.loadPropertyData();
},

// 预留：加载更多数据
onLoadMore() {
  console.log('加载更多数据');

  // TODO: 预留加载更多API接口调用
  /*
  wx.request({
    url: 'https://your-api.com/api/property/loadmore',
    method: 'POST',
    data: {
      page: this.data.currentPage + 1,
      filter: this.data.currentFilter,
      keyword: this.data.searchKeyword
    },
    success: (res) => {
      console.log('加载更多数据成功:', res.data);
      const newList = this.data.propertyList.concat(res.data.list || []);
      this.setData({
        propertyList: newList,
        currentPage: this.data.currentPage + 1
      });
    },
    fail: (err) => {
      console.error('加载更多数据失败:', err);
    }
  });
  */
},

// 页面显示时触发
onShow() {
  console.log('页面显示');
  // 可以在这里刷新数据或更新状态
},

// 页面隐藏时触发
onHide() {
  console.log('页面隐藏');
},

// 页面卸载时触发
onUnload() {
  console.log('页面卸载');
}
})
