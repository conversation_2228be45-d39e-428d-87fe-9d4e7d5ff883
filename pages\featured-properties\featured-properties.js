// pages/featured-properties/featured-properties.js
const api = require('../../config/api.js')
const util = require('../../utils/util.js')

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 页面加载状态
    loading: true,
    refreshing: false,
    
    // 搜索相关
    searchKeyword: '',
    searchPlaceholder: '搜索小区名称',
    isSearching: false,
    originalPropertyList: [], // 备份原始数据
    
    // 筛选条件
    filterOptions: {
      priceRange: ['户型报价', '更多', '排序'],
      tags: ['严选好房', '新上房源', '降价急售']
    },
    selectedPriceIndex: 0,
    selectedTagIndex: 0,
    
    // 房源列表数据
    propertyList: [
      {
        id: 1,
        title: '富力中心玖誉',
        subtitle: '4室2厅',
        badge: '笋盘精选',
        valueTag: '宝安1200万以上价值榜 No.5',
        area: '220平',
        direction: '西南',
        floor: '中/共44层',
        location: '宝安宝安宝安中心',
        totalPrice: '2630万',
        unitPrice: '11.95万/平',
        priceDown: '低于同户型售价500万',
        image: '/image/house1.jpg',
        videoIcon: true,
        reasons: [
          '2600万以上宝安中心标杆豪宅享受型4房',
          '宝中顶豪，顶级配套，南北通透，海天九年制学校...'
        ],
        timeAgo: '12分钟前入选'
      },
      {
        id: 2,
        title: '熙龙湾一期',
        subtitle: '5室2厅',
        badge: '笋盘精选',
        valueTag: '',
        area: '149.17平',
        direction: '西南',
        floor: '高/共32层',
        location: '宝安宝安宝安中心',
        totalPrice: '1798万',
        unitPrice: '12.05万/平',
        priceDown: '低于同户型售价120万',
        image: '/image/house2.jpg',
        videoIcon: false,
        reasons: [
          '宝中豪宅单价12万 熙龙湾最低价4+1房 高层送',
          '套内158平 得房率105% 高层赠送厨房五年...'
        ],
        timeAgo: '15分钟前入选',
        tags: ['板楼南北通透', '超大赠送面积']
      },
      {
        id: 3,
        title: '汉京山',
        subtitle: '2室2厅',
        badge: '笋盘精选',
        valueTag: '南山中心400-500万价值榜 No.3',
        area: '58平',
        direction: '西南',
        floor: '高/共33层',
        location: '南山南山中心',
        totalPrice: '435万',
        unitPrice: '7.5万/平',
        priceDown: '低于同户型售价55万',
        image: '/image/house3.jpg',
        videoIcon: false,
        reasons: [
          '同户型最低价出售，稀缺2房2厅',
          '高层南向地铁口房源超过100%物业，停车位充足...'
        ],
        timeAgo: '1小时前入选'
      }
    ],
    
    // 分页相关
    currentPage: 1,
    pageSize: 10,
    hasMore: true,
    
    // API接口配置
    apiEndpoints: {
      getPropertyList: '/api/properties/featured',
      searchProperties: '/api/properties/search',
      getPropertyDetail: '/api/properties/detail'
    }
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    console.log('笋盘精选页面加载');
    this.loadPropertyList();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    // 页面显示时刷新数据
    if (!this.data.loading) {
      this.refreshPropertyList();
    }
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh: function () {
    console.log('下拉刷新');
    this.setData({
      refreshing: true
    });
    this.refreshPropertyList();
  },

  /**
   * 上拉加载更多
   */
  onReachBottom: function () {
    console.log('上拉加载更多');
    if (this.data.hasMore && !this.data.loading) {
      this.loadMoreProperties();
    }
  },

  /**
   * 搜索输入处理
   */
  onSearchInput: function(e) {
    const keyword = e.detail.value;
    this.setData({
      searchKeyword: keyword
    });

    // 如果搜索框为空，恢复原始数据
    if (keyword.trim() === '') {
      this.clearSearch();
    }
  },

  /**
   * 搜索确认
   */
  onSearchConfirm: function(e) {
    const keyword = e.detail.value.trim();
    if (keyword) {
      this.searchProperties(keyword);
    } else {
      this.clearSearch();
    }
  },

  /**
   * 筛选条件点击
   */
  onFilterTap: function(e) {
    const type = e.currentTarget.dataset.type;
    const index = e.currentTarget.dataset.index;
    
    console.log('筛选条件点击:', type, index);
    
    if (type === 'price') {
      this.setData({
        selectedPriceIndex: index
      });
      this.applyPriceFilter(index);
    } else if (type === 'tag') {
      this.setData({
        selectedTagIndex: index
      });
      this.applyTagFilter(index);
    }
  },

  /**
   * 房源卡片点击
   */
  onPropertyTap: function(e) {
    const propertyId = e.currentTarget.dataset.id;
    console.log('点击房源:', propertyId);
    
    // 跳转到房源详情页
    wx.navigateTo({
      url: `/pages/property-detail/property-detail?id=${propertyId}`
    });
  },

  /**
   * 视频播放点击
   */
  onVideoTap: function(e) {
    const propertyId = e.currentTarget.dataset.id;
    console.log('播放视频:', propertyId);
    
    wx.showToast({
      title: '播放房源视频',
      icon: 'none'
    });
  },

  /**
   * 加载房源列表
   */
  loadPropertyList: function() {
    console.log('加载房源列表');

    this.setData({
      loading: true
    });

    // 模拟API调用
    setTimeout(() => {
      // 备份原始数据
      if (this.data.originalPropertyList.length === 0) {
        this.setData({
          originalPropertyList: [...this.data.propertyList]
        });
      }

      this.setData({
        loading: false,
        refreshing: false
      });

      wx.stopPullDownRefresh();
    }, 1000);
  },

  /**
   * 刷新房源列表
   */
  refreshPropertyList: function() {
    console.log('刷新房源列表');
    
    this.setData({
      currentPage: 1,
      hasMore: true
    });
    
    this.loadPropertyList();
  },

  /**
   * 加载更多房源
   */
  loadMoreProperties: function() {
    console.log('加载更多房源');
    
    if (!this.data.hasMore) {
      return;
    }
    
    this.setData({
      loading: true
    });
    
    // 模拟API调用
    setTimeout(() => {
      const newPage = this.data.currentPage + 1;
      
      // 模拟没有更多数据
      if (newPage > 3) {
        this.setData({
          hasMore: false,
          loading: false
        });
        
        wx.showToast({
          title: '没有更多数据了',
          icon: 'none'
        });
        return;
      }
      
      // 模拟新数据
      const newProperties = this.generateMockProperties(3);
      const updatedList = [...this.data.propertyList, ...newProperties];
      
      this.setData({
        propertyList: updatedList,
        currentPage: newPage,
        loading: false
      });
    }, 1000);
  },

  /**
   * 搜索房源
   */
  searchProperties: function(keyword) {
    console.log('搜索房源:', keyword);

    if (!keyword || keyword.trim() === '') {
      this.clearSearch();
      return;
    }

    util.showLoading('搜索中...');

    // 设置搜索状态
    this.setData({
      isSearching: true
    });

    // 模拟API调用
    setTimeout(() => {
      util.hideLoading();

      // 从原始数据中搜索
      const searchData = this.data.originalPropertyList.length > 0
        ? this.data.originalPropertyList
        : this.data.propertyList;

      // 多字段搜索
      const filteredProperties = searchData.filter(property => {
        const searchText = keyword.toLowerCase();
        return (
          property.title.toLowerCase().includes(searchText) ||
          property.location.toLowerCase().includes(searchText) ||
          property.subtitle.toLowerCase().includes(searchText) ||
          (property.valueTag && property.valueTag.toLowerCase().includes(searchText)) ||
          property.reasons.some(reason => reason.toLowerCase().includes(searchText))
        );
      });

      if (filteredProperties.length === 0) {
        util.showToast('未找到相关房源');
        // 即使没有结果也要更新列表为空
        this.setData({
          propertyList: []
        });
      } else {
        this.setData({
          propertyList: filteredProperties
        });
        util.showSuccess(`找到${filteredProperties.length}套房源`);
      }
    }, 800);
  },

  /**
   * 清空搜索
   */
  clearSearch: function() {
    console.log('清空搜索');

    this.setData({
      searchKeyword: '',
      isSearching: false,
      propertyList: [...this.data.originalPropertyList]
    });
  },

  /**
   * 应用价格筛选
   */
  applyPriceFilter: function(index) {
    console.log('应用价格筛选:', index);

    // 这里可以根据index应用不同的价格筛选逻辑
    util.showToast('价格筛选功能');
  },

  /**
   * 应用标签筛选
   */
  applyTagFilter: function(index) {
    console.log('应用标签筛选:', index);
    
    // 这里可以根据index应用不同的标签筛选逻辑
    util.showToast('标签筛选功能');
  },

  /**
   * 生成模拟房源数据
   */
  generateMockProperties: function(count) {
    const mockProperties = [];
    const titles = ['万科城', '恒大名都', '碧桂园', '保利花园', '中海锦城'];
    const locations = ['南山中心', '福田中心', '宝安中心', '龙华中心', '龙岗中心'];
    
    for (let i = 0; i < count; i++) {
      mockProperties.push({
        id: Date.now() + i,
        title: titles[i % titles.length],
        subtitle: `${Math.floor(Math.random() * 3) + 2}室${Math.floor(Math.random() * 2) + 1}厅`,
        badge: '笋盘精选',
        valueTag: '',
        area: `${Math.floor(Math.random() * 100) + 50}平`,
        direction: '南北',
        floor: `中/共${Math.floor(Math.random() * 20) + 20}层`,
        location: locations[i % locations.length],
        totalPrice: `${Math.floor(Math.random() * 1000) + 500}万`,
        unitPrice: `${(Math.random() * 5 + 5).toFixed(1)}万/平`,
        priceDown: `低于同户型售价${Math.floor(Math.random() * 100) + 20}万`,
        image: `/image/house${(i % 3) + 1}.jpg`,
        videoIcon: Math.random() > 0.5,
        reasons: [
          '优质房源，性价比高',
          '地理位置优越，交通便利'
        ],
        timeAgo: `${Math.floor(Math.random() * 60) + 1}分钟前入选`
      });
    }
    
    return mockProperties;
  },

  /**
   * 分享功能
   */
  onShareAppMessage: function () {
    return {
      title: '荣杏深房 - 笋盘精选',
      path: '/pages/featured-properties/featured-properties',
      imageUrl: '/image/sunpan.png'
    };
  }
});
