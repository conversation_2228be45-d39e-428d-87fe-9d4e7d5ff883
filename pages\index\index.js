//index.js
Page({
  data: {
    
  },

  onLoad() {
    
  },

  // 工具点击事件
  onToolTap(e) {
    const type = e.currentTarget.dataset.type;
    console.log('点击工具:', type);
    
    switch(type) {
      case 'data':
        console.log('准备跳转到成交数据页面');
        wx.switchTab({
          url: '/pages/tools/tools',
          success: function(res) {
            console.log('跳转成功', res);
          },
          fail: function(err) {
            console.error('跳转失败', err);
            wx.showToast({
              title: '跳转失败: ' + err.errMsg,
              icon: 'none',
              duration: 3000
            });
          }
        });
        break;
      case 'calculator':
        console.log('准备跳转到房贷计算器页面');
        wx.navigateTo({
          url: '/pages/calculator/calculator',
          success: function(res) {
            console.log('跳转成功', res);
          },
          fail: function(err) {
            console.error('跳转失败', err);
            wx.showToast({
              title: '跳转失败: ' + err.errMsg,
              icon: 'none',
              duration: 3000
            });
          }
        });
        break;
      case 'evaluate':
        console.log('准备跳转到房产评估页面');
        wx.navigateTo({
          url: '/pages/loan-consultation/loan-consultation',
          success: function(res) {
            console.log('跳转成功', res);
          },
          fail: function(err) {
            console.error('跳转失败', err);
            wx.showToast({
              title: '跳转失败: ' + err.errMsg,
              icon: 'none',
              duration: 3000
            });
          }
        });
        break;
    }
  },

  // 服务点击事件
  onServiceTap(e) {
    const type = e.currentTarget.dataset.type;
    console.log('点击服务:', type);
    
    switch(type) {
      case 'featured-properties':
        wx.navigateTo({
          url: '/pages/featured-properties/featured-properties'
        });
        break;
      case 'buy-consult':
        wx.navigateTo({
          url: '/pages/house-consultation/house-consultation'
        });
        break;
      case 'loan-consult':
        wx.navigateTo({
          url: '/pages/property-evaluation/property-evaluation'
        });
        break;
      case 'sell-house':
        wx.navigateTo({
          url: '/pages/planet-member/planet-member'
        });
        break;
      case 'expert':
        wx.showToast({
          title: '专家精选',
          icon: 'none'
        });
        break;
      case 'planet-member':
        wx.navigateTo({
          url: '/pages/sell-house/sell-house'
        });
        break;
      case 'my-orders':
        wx.navigateTo({
          url: '/pages/my-orders/my-orders'
        });
        break;
    }
  }
})
