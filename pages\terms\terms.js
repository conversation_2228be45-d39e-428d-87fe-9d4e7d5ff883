//terms.js
Page({
  data: {
    currentTab: 0, // 当前选中的标签页

    // 片区分析数据
    areaAnalysis: [
      {
        id: 1,
        title: '香蜜湖片区分析',
        subtitle: '',
        date: '荣杏   2024/11/24',
        liop:'2300+',
      },
      {
        id: 2,
        title: '华侨城片区分析',
        subtitle: '|            深圳楼市买房攻略',
        date: '荣杏 7/12/24'
      },
      {
        id: 3,
        title: '深圳湾片区分析',
        subtitle: '|            深圳楼市买房攻略',
        date: '荣杏 7/26/24'
      },
      {
        id: 4,
        title: '后海片区分析',
        subtitle: '|            深圳楼市买房攻略',
        date: '荣杏 6/25/24'
      },
      {
        id: 5,
        title: '前海片区分析',
        subtitle: '|            深圳楼市买房攻略',
        date: '荣杏 6/10/24'
      },
      {
        id: 6,
        title: '南山科技园片区分析',
        subtitle: '|            深圳楼市买房攻略',
        date: '荣杏 6/18/24'
      },
      {
        id: 7,
        title: '蛇口片区分析',
        subtitle: '|            深圳楼市买房攻略',
        date: '荣杏 5/22/24'
      },
      {
        id: 8,
        title: '宝中片区分析',
        subtitle: '|            深圳楼市买房攻略',
        date: '荣杏 8/14/24'
      }
    ],

    // 楼盘评测数据
    buildingEvaluation: [
      {
        id: 1,
        title: '万科云城评测',
        subtitle: '|            深圳楼市买房攻略',
        date: '荣杏 7/15/24'
      },
      {
        id: 2,
        title: '招商蛇口太子湾',
        subtitle: '|            深圳楼市买房攻略',
        date: '荣杏 7/20/24'
      },
      {
        id: 3,
        title: '华润城润府',
        subtitle: '|            深圳楼市买房攻略',
        date: '荣杏 7/25/24'
      }
    ],

    // 学位分析数据
    schoolAnalysis: [
      {
        id: 1,
        title: '南山实验学校学位房',
        subtitle: '|            深圳楼市买房攻略',
        date: '荣杏 2024/12/24'
      },
      {
        id: 2,
        title: '深圳中学学位房',
        subtitle: '|            深圳楼市买房攻略',
        date: '荣杏 7/18/24'
      },
      {
        id: 3,
        title: '荔园小学学位房',
        subtitle: '|            深圳楼市买房攻略',
        date: '荣杏 7/22/24'
      }
    ]
  },

  onLoad() {
    console.log('买房攻略页面加载完成');
  },

  // 切换标签页
  switchTab(e) {
    const index = parseInt(e.currentTarget.dataset.index);
    this.setData({
      currentTab: index
    });

    console.log('切换到标签页:', index);

    // 预留：可以在这里添加数据加载逻辑
    this.loadTabData(index);
  },

  // 加载标签页数据（预留API接口调用位置）
  loadTabData(tabIndex) {
    const tabNames = ['片区分析', '楼盘评测', '学位分析'];
    console.log(`加载${tabNames[tabIndex]}数据`);

    // TODO: 预留API接口调用位置
    // 后续可以在这里调用微信公众号API获取数据
    /*
    switch(tabIndex) {
      case 0:
        this.loadAreaAnalysisData();
        break;
      case 1:
        this.loadBuildingEvaluationData();
        break;
      case 2:
        this.loadSchoolAnalysisData();
        break;
    }
    */
  },

  // 内容项点击事件
  onItemTap(e) {
    const { type, item } = e.currentTarget.dataset;

    console.log('点击内容项:', type, item);

    // 显示点击反馈
    wx.showToast({
      title: `查看${item.title}`,
      icon: 'none',
      duration: 1500
    });

    // 预留：API接口调用位置
    this.callWeChatAPI(type, item);
  },

  // 调用微信公众号API（预留接口）
  callWeChatAPI(type, item) {
    console.log('准备调用微信公众号API:', type, item);

    // TODO: 预留微信公众号API接口调用
    // 这里可以根据不同类型调用不同的API接口
    /*
    const apiConfig = {
      area: '/api/area-analysis',
      building: '/api/building-evaluation',
      school: '/api/school-analysis'
    };

    const apiUrl = apiConfig[type];
    if (apiUrl) {
      wx.request({
        url: `https://your-wechat-api.com${apiUrl}`,
        method: 'POST',
        data: {
          id: item.id,
          title: item.title,
          type: type
        },
        success: (res) => {
          console.log('API调用成功:', res.data);
          // 处理API返回数据
        },
        fail: (err) => {
          console.error('API调用失败:', err);
        }
      });
    }
    */
  },

  // 预留：片区分析数据加载
  loadAreaAnalysisData() {
    // TODO: 调用片区分析API
    console.log('加载片区分析数据');
  },

  // 预留：楼盘评测数据加载
  loadBuildingEvaluationData() {
    // TODO: 调用楼盘评测API
    console.log('加载楼盘评测数据');
  },

  // 预留：学位分析数据加载
  loadSchoolAnalysisData() {
    // TODO: 调用学位分析API
    console.log('加载学位分析数据');
  }
})
